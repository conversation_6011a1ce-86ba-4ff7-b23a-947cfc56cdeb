import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:dio/dio.dart';
import '../medstata_api.dart';
import 'lab_test_detail_screen.dart';
import 'document_viewer_screen.dart';

class LabReportDetailScreen extends StatefulWidget {
  final String reportId;

  const LabReportDetailScreen({
    super.key,
    required this.reportId,
  });

  @override
  State<LabReportDetailScreen> createState() => _LabReportDetailScreenState();
}

class _LabReportDetailScreenState extends State<LabReportDetailScreen> {
  final LabReportService _labReportService = LabReportService();
  LabReportDetail? _reportDetail;
  bool _isLoading = true;
  String? _errorMessage;
  int _selectedTabIndex = 0; // 0 - Результаты, 1 - Документ
  Timer? _refreshTimer;

  @override
  void initState() {
    super.initState();
    _loadReportDetail();
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    _labReportService.dispose();
    super.dispose();
  }

  Future<void> _loadReportDetail() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final response = await _labReportService.getLabReportDetail(widget.reportId);

      if (response.isSuccess && response.data != null) {
        // Добавляем логирование загруженных данных
        final reportDetail = response.data!;
        //todo: remove
        print('✅ LabReportDetailScreen._loadReportDetail: Данные загружены успешно');
        print('   response.data: ${response.data}');
        print('   Report ID: ${reportDetail.report.id}');
        print('   Report Status: ${reportDetail.report.status}');
        print('   Document: ${reportDetail.report.document != null ? "ЕСТЬ" : "НЕТ"}');
        if (reportDetail.report.document != null) {
          final doc = reportDetail.report.document!;
          print('   Document details:');
          print('     originalFileName: ${doc.originalFileName}');
          print('     fileType: ${doc.fileType}');
          print('     fileSizeBytes: ${doc.fileSizeBytes}');
        }

        setState(() {
          _reportDetail = response.data;
          _isLoading = false;
        });
        _manageRefreshTimer();
      } else if (response.isNoData) {
        setState(() {
          _errorMessage = 'Отчет не найден';
          _isLoading = false;
        });
        _stopRefreshTimer();
      } else {
        setState(() {
          _errorMessage = response.message ?? 'Ошибка загрузки отчета';
          _isLoading = false;
        });
        _stopRefreshTimer();
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Ошибка при загрузке отчета: $e';
        _isLoading = false;
      });
      _stopRefreshTimer();
    }
  }

  /// Управляет Timer'ом в зависимости от статуса отчета
  void _manageRefreshTimer() {
    if (_reportDetail == null) return;

    final status = _reportDetail!.report.status;

    // Запускаем Timer только для статусов pending и processing
    if (status == LabReportStatus.pending || status == LabReportStatus.processing) {
      _startRefreshTimer();
    } else {
      // Останавливаем Timer для completed и failed
      _stopRefreshTimer();
    }
  }

  /// Запускает Timer для автоматического обновления каждые 3 секунды
  void _startRefreshTimer() {
    _stopRefreshTimer(); // Останавливаем предыдущий Timer если есть

    _refreshTimer = Timer.periodic(const Duration(seconds: 3), (timer) {
      if (mounted) {
        _refreshReportData();
      } else {
        timer.cancel();
      }
    });
  }

  /// Останавливает Timer автоматического обновления
  void _stopRefreshTimer() {
    _refreshTimer?.cancel();
    _refreshTimer = null;
  }

  /// Обновляет данные отчета без показа индикатора загрузки
  Future<void> _refreshReportData() async {
    try {
      final response = await _labReportService.getLabReportDetail(widget.reportId);

      if (response.isSuccess && response.data != null) {
        final newReportDetail = response.data!;
        final oldStatus = _reportDetail?.report.status;
        final newStatus = newReportDetail.report.status;

        setState(() {
          _reportDetail = newReportDetail;
          _errorMessage = null;
        });

        // Если статус изменился, обновляем управление Timer'ом
        if (oldStatus != newStatus) {
          _manageRefreshTimer();
        }
      }
    } catch (e) {
      // Не показываем ошибки при фоновом обновлении
      print('Ошибка фонового обновления отчета: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text(
          'Результаты',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.red),
          onPressed: () => Navigator.pop(context),
        ),
        actions: _shouldShowActions()
            ? [
                IconButton(
                  icon: const Icon(Icons.edit, color: Colors.red),
                  onPressed: () {
                    // TODO: Implement edit functionality
                  },
                ),
              ]
            : null,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator(color: Colors.red))
          : _errorMessage != null
              ? _buildErrorState()
              : _reportDetail != null
                  ? _buildContentBasedOnStatus()
                  : const Center(child: Text('Нет данных')),
    );
  }

  bool _shouldShowActions() {
    if (_reportDetail == null) return false;
    final status = _reportDetail!.report.status;
    return status == LabReportStatus.completed;
  }

  Widget _buildContentBasedOnStatus() {
    final status = _reportDetail!.report.status;

    switch (status) {
      case LabReportStatus.pending:
      case LabReportStatus.processing:
        return _buildProcessingState();
      case LabReportStatus.failed:
        return _buildFailedState();
      case LabReportStatus.completed:
        return _buildContent();
    }
  }

  Widget _buildProcessingState() {
    return Stack(
      children: [
        // Скелетон-контент
        _buildSkeletonContent(),

        // Полупрозрачный оверлей
        Container(
          color: Colors.white.withOpacity(0.6),
        ),

        // Центр: крутилка + текст
        Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(
                width: 48,
                height: 48,
                child: CircularProgressIndicator(
                  color: Colors.grey,
                  strokeWidth: 4,
                ),
              ),
              const SizedBox(height: 24),
              const Text(
                'Идет обработка анализов...',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.black,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSkeletonContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Скелетон профиля
          _buildSkeletonProfile(),
          const SizedBox(height: 24),

          // Скелетон информации
          _buildSkeletonInfo(),
          const SizedBox(height: 24),

          // Скелетон табов
          _buildSkeletonTabs(),
          const SizedBox(height: 16),

          // Скелетон контента
          _buildSkeletonResults(),
        ],
      ),
    );
  }

  Widget _buildSkeletonProfile() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Аватар скелетон
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: const Color(0xFFD0D0D0),
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 16),

          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Заголовок "Профиль"
                Container(
                  width: 80,
                  height: 20,
                  decoration: BoxDecoration(
                    color: const Color(0xFFD0D0D0),
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                const SizedBox(height: 8),

                // Возраст
                Container(
                  width: 60,
                  height: 16,
                  decoration: BoxDecoration(
                    color: const Color(0xFFD0D0D0),
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSkeletonInfo() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildSkeletonInfoRow('Дата'),
          const SizedBox(height: 16),
          _buildSkeletonInfoRow('Лаборатория'),
          const SizedBox(height: 16),
          _buildSkeletonInfoRow('Заметки'),
        ],
      ),
    );
  }

  Widget _buildSkeletonInfoRow(String label) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey.shade800,
          ),
        ),
        Container(
          width: 120,
          height: 16,
          decoration: BoxDecoration(
            color: const Color(0xFFD0D0D0),
            borderRadius: BorderRadius.circular(4),
          ),
        ),
      ],
    );
  }

  Widget _buildSkeletonTabs() {
    return Row(
      children: [
        // Активная вкладка "Результаты"
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          decoration: BoxDecoration(
            color: Colors.red.shade50,
            borderRadius: BorderRadius.circular(25),
            border: Border.all(color: Colors.red.shade200),
          ),
          child: Text(
            'Результаты',
            style: TextStyle(
              color: Colors.red,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        const SizedBox(width: 12),

        // Неактивная вкладка "Документ"
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          decoration: BoxDecoration(
            color: Colors.grey.shade100,
            borderRadius: BorderRadius.circular(25),
          ),
          child: Text(
            'Документ',
            style: TextStyle(
              color: Colors.grey.shade600,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSkeletonResults() {
    return Column(
      children: List.generate(6, (index) =>
        Container(
          margin: const EdgeInsets.only(bottom: 12),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: const Color(0xFFD0D0D0)),
          ),
          child: Row(
            children: [
              Expanded(
                child: Container(
                  height: 16,
                  decoration: BoxDecoration(
                    color: const Color(0xFFD0D0D0),
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Container(
                width: 60,
                height: 16,
                decoration: BoxDecoration(
                  color: const Color(0xFFD0D0D0),
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
              const SizedBox(width: 8),
              Container(
                width: 20,
                height: 20,
                decoration: BoxDecoration(
                  color: const Color(0xFFD0D0D0),
                  shape: BoxShape.circle,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFailedState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Иконка ошибки
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: Colors.red.shade50,
                borderRadius: BorderRadius.circular(40),
              ),
              child: Icon(
                Icons.error_outline,
                size: 48,
                color: Colors.red,
              ),
            ),
            const SizedBox(height: 24),

            // Заголовок
            Text(
              'Ошибка при обработке анализа',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.w600,
                color: Colors.grey.shade800,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),

            // Описание
            Text(
              'Произошла ошибка при обработке вашего анализа.\nПожалуйста, попробуйте еще раз или обратитесь в поддержку.',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey.shade600,
                height: 1.5,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),

            // Кнопки действий
            Column(
              children: [
                // Кнопка "Повторить"
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {
                      _loadReportDetail();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.refresh, size: 20),
                        const SizedBox(width: 8),
                        Text(
                          'Повторить',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 12),

                // Кнопка "Обратиться в поддержку"
                SizedBox(
                  width: double.infinity,
                  child: OutlinedButton(
                    onPressed: () {
                      _contactSupport();
                    },
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.red,
                      side: BorderSide(color: Colors.red),
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.support_agent, size: 20),
                        const SizedBox(width: 8),
                        Text(
                          'Обратиться в поддержку',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 32),

            // Дополнительная информация
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.red.shade50,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.red.shade200),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: Colors.red.shade700,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'Если проблема повторяется, наша служба поддержки поможет вам разобраться',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.red.shade700,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _contactSupport() {
    // TODO: Implement support contact functionality
    // Можно открыть email, телефон, или чат поддержки
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Поддержка'),
        content: Text('Функция обращения в поддержку будет реализована позже.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('OK'),
          ),
        ],
      ),
    );
  }

  /// Скачивает документ лабораторного отчета
  Future<void> _downloadDocument(LabReportDocument document) async {
    try {
      // Показываем индикатор загрузки
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('Скачивание документа...'),
            ],
          ),
        ),
      );

      // Получаем временную ссылку на документ
      print('📥 Запрашиваем ссылку на документ для отчета: ${widget.reportId}');
      final urlResponse = await _labReportService.getLabReportDocumentUrl(widget.reportId);

      print('📥 Ответ API: status=${urlResponse.status}, isSuccess=${urlResponse.isSuccess}');
      print('📥 Данные ответа: ${urlResponse.data}');

      if (!urlResponse.isSuccess || urlResponse.data == null || urlResponse.data!.isEmpty) {
        Navigator.pop(context); // Закрываем диалог загрузки
        print('❌ Ошибка получения ссылки: status=${urlResponse.status}, data=${urlResponse.data}');
        _showErrorDialog('Не удалось получить ссылку на документ. Статус: ${urlResponse.status}');
        return;
      }

      final documentUrl = urlResponse.data!;
      print('📥 Скачиваем документ по ссылке: $documentUrl');

      // Получаем директорию для сохранения
      final directory = await getApplicationDocumentsDirectory();
      final fileName = document.originalFileName ?? 'document.${_getFileExtension(document.fileType)}';
      final filePath = '${directory.path}/$fileName';

      // Скачиваем файл
      final dio = Dio();
      await dio.download(documentUrl, filePath);

      Navigator.pop(context); // Закрываем диалог загрузки

      // Показываем сообщение об успешном скачивании
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Документ сохранен: $fileName'),
          backgroundColor: Colors.green,
          action: SnackBarAction(
            label: 'Открыть папку',
            textColor: Colors.white,
            onPressed: () => _openFileLocation(filePath),
          ),
        ),
      );

    } catch (e) {
      Navigator.pop(context); // Закрываем диалог загрузки если он открыт
      print('❌ Ошибка скачивания документа: $e');
      _showErrorDialog('Ошибка при скачивании документа: $e');
    }
  }

  /// Показывает превью документа в встроенном WebView
  Future<void> _previewDocument(LabReportDocument document) async {
    try {
      // Показываем индикатор загрузки
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('Загрузка документа...'),
            ],
          ),
        ),
      );

      // Получаем временную ссылку на документ
      print('👁️ Запрашиваем ссылку на документ для просмотра: ${widget.reportId}');
      final urlResponse = await _labReportService.getLabReportDocumentUrl(widget.reportId);

      print('👁️ Ответ API: status=${urlResponse.status}, isSuccess=${urlResponse.isSuccess}');
      print('👁️ Данные ответа: ${urlResponse.data}');

      if (!urlResponse.isSuccess || urlResponse.data == null || urlResponse.data!.isEmpty) {
        Navigator.pop(context); // Закрываем диалог загрузки
        print('❌ Ошибка получения ссылки для просмотра: status=${urlResponse.status}, data=${urlResponse.data}');
        _showErrorDialog('Не удалось получить ссылку на документ. Статус: ${urlResponse.status}');
        return;
      }

      final documentUrl = urlResponse.data!;
      print('👁️ Открываем превью документа в WebView по ссылке: $documentUrl');

      Navigator.pop(context); // Закрываем диалог загрузки

      // Открываем документ в встроенном WebView
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => DocumentViewerScreen(
            documentUrl: documentUrl,
            documentName: document.originalFileName ?? 'Документ',
          ),
        ),
      );

    } catch (e) {
      Navigator.pop(context); // Закрываем диалог загрузки если он открыт
      print('❌ Ошибка открытия документа: $e');
      _showErrorDialog('Ошибка при открытии документа: $e');
    }
  }

  /// Показывает диалог с ошибкой
  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Ошибка'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// Получает расширение файла по типу
  String _getFileExtension(String? fileType) {
    if (fileType == null) return 'bin';

    switch (fileType.toLowerCase()) {
      case 'pdf':
        return 'pdf';
      case 'jpg':
      case 'jpeg':
        return 'jpg';
      case 'png':
        return 'png';
      case 'gif':
        return 'gif';
      default:
        return 'bin';
    }
  }

  /// Открывает папку с файлом (попытка)
  Future<void> _openFileLocation(String filePath) async {
    try {
      // На iOS/Android это может не работать, но попробуем
      final directory = File(filePath).parent;
      final uri = Uri.file(directory.path);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
      }
    } catch (e) {
      print('Не удалось открыть папку: $e');
    }
  }

  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            _errorMessage!,
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadReportDetail,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Повторить'),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    final report = _reportDetail!.report;
    
    return Column(
      children: [
        _buildHeader(report),
        _buildTabBar(),
        Expanded(child: _buildTabContent()),
      ],
    );
  }

  Widget _buildHeader(LabReport report) {
    final parts = [
      _reportDetail?.patientGender,
      _reportDetail?.patientAge,
    ].where((e) => e != null && e.isNotEmpty).toList();

    final description = parts.join(', ');

    return Container(
      color: Colors.white,
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Профиль
          Row(
            children: [
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color: Colors.red.shade100,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.person,
                  color: Colors.red.shade600,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              const Expanded(
                child: Text(
                  'Профиль',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              Text(
                description, // Показываем пол и возраст пациента
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // Дата
          Row(
            children: [
              const SizedBox(width: 62), // Отступ как у профиля
              const Expanded(
                child: Text(
                  'Дата',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              Text(
                report.formattedPerformedDate,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // Лаборатория
          Row(
            children: [
              const SizedBox(width: 62), // Отступ как у профиля
              const SizedBox(
                width: 120,
                child: Text(
                  'Лаборатория',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              const SizedBox(width: 8), // небольшой отступ между колонками (по желанию)
              Expanded(
                child: Text(
                  report.laboratoryName ?? 'Не указана',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey.shade600,
                  ),
                  overflow: TextOverflow.fade,
                  softWrap: false,
                  textAlign: TextAlign.right, // ← прижатие к правому краю
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Заметки
          Row(
            children: [
              const SizedBox(width: 62), // Отступ как у профиля
              const Expanded(
                child: Text(
                  'Заметки',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              Icon(
                Icons.chevron_right,
                color: Colors.grey.shade400,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade200,
        borderRadius: BorderRadius.circular(25),
      ),
      child: Row(
        children: [
          Expanded(
            child: GestureDetector(
              onTap: () {
                setState(() {
                  _selectedTabIndex = 0;
                });
              },
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: _selectedTabIndex == 0
                    ? BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(25),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      )
                    : null,
                child: Text(
                  'Результаты',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontWeight: _selectedTabIndex == 0 ? FontWeight.w600 : FontWeight.w500,
                    fontSize: 16,
                    color: _selectedTabIndex == 0 ? Colors.black : Colors.grey.shade600,
                  ),
                ),
              ),
            ),
          ),
          Expanded(
            child: GestureDetector(
              onTap: () {
                setState(() {
                  _selectedTabIndex = 1;
                });
              },
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: _selectedTabIndex == 1
                    ? BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(25),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      )
                    : null,
                child: Text(
                  'Документ',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontWeight: _selectedTabIndex == 1 ? FontWeight.w600 : FontWeight.w500,
                    fontSize: 16,
                    color: _selectedTabIndex == 1 ? Colors.black : Colors.grey.shade600,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabContent() {
    if (_selectedTabIndex == 0) {
      return _buildResultsTab();
    } else {
      return _buildDocumentTab();
    }
  }

  Widget _buildResultsTab() {
    final tests = _reportDetail!.tests;
    
    return Container(
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Padding(
            padding: EdgeInsets.all(16),
            child: Text(
              'Биомаркеры',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: tests.length,
              itemBuilder: (context, index) {
                final test = tests[index];
                return _buildTestItem(test);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTestItem(LabTest test) {
    final statusColor = _getStatusColor(test.status);
    final displayValue = _getDisplayValue(test);

    return InkWell(
      onTap: () => _showTestDetail(test),
      borderRadius: BorderRadius.circular(12),
      child: Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey.shade200),
        ),
        child: Row(
          children: [
            Expanded(
              child: Text(
                test.name,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            Row(
              children: [
                Text(
                  displayValue,
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey.shade600,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(width: 8),
                SizedBox(
                  width: 40,
                  child: Align(
                    alignment: Alignment.centerRight,
                    child: _buildStatusIndicator(test, statusColor),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDocumentTab() {
    final document = _reportDetail?.report.document;

    // Добавляем детальное логирование для отладки
    print('🗂️ LabReportDetailScreen._buildDocumentTab: Проверяем документ');
    print('   _reportDetail: ${_reportDetail != null ? "НЕ NULL" : "NULL"}');
    if (_reportDetail != null) {
      print('   _reportDetail.report: ${_reportDetail!.report}');
      print('   _reportDetail.report.document: ${_reportDetail!.report.document}');
      if (_reportDetail!.report.document != null) {
        final doc = _reportDetail!.report.document!;
        print('   document.originalFileName: ${doc.originalFileName}');
        print('   document.fileType: ${doc.fileType}');
        print('   document.fileSizeBytes: ${doc.fileSizeBytes}');
      }
    }

    if (document == null) {
      print('❌ LabReportDetailScreen._buildDocumentTab: Документ отсутствует, показываем заглушку');
      return Container(
        color: Colors.white,
        child: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.description_outlined,
                size: 64,
                color: Colors.grey,
              ),
              SizedBox(height: 16),
              Text(
                'Документ недоступен',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
        ),
      );
    }

    print('✅ LabReportDetailScreen._buildDocumentTab: Документ найден, отображаем информацию');

    return Container(
      color: Colors.white,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDocumentHeader(document),
            const SizedBox(height: 24),
            _buildDocumentInfo(document),
            const SizedBox(height: 24),
            _buildDocumentActions(document),
          ],
        ),
      ),
    );
  }

  Widget _buildDocumentHeader(LabReportDocument document) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Row(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: Colors.blue.shade100,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Center(
              child: Text(
                document.fileTypeIcon,
                style: const TextStyle(fontSize: 24),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  document.originalFileName ?? 'Неизвестный файл',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Text(
                  '${document.fileType ?? 'Неизвестный тип'} • ${document.formattedFileSize}',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDocumentInfo(LabReportDocument document) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Информация о документе',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 16),
        _buildInfoRow('Имя файла', document.originalFileName ?? 'Не указано'),
        _buildInfoRow('Тип файла', document.fileType ?? 'Не указан'),
        _buildInfoRow('Размер файла', document.formattedFileSize),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDocumentActions(LabReportDocument document) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Действия',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),

        const SizedBox(height: 16),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: () => _previewDocument(document),
            icon: const Icon(Icons.visibility),
            label: const Text('Просмотреть документ'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),

        // const SizedBox(height: 16),
        // SizedBox(
        //   width: double.infinity,
        //   child: ElevatedButton.icon(
        //     onPressed: () => _downloadDocument(document),
        //     icon: const Icon(Icons.download),
        //     label: const Text('Скачать документ'),
        //     style: ElevatedButton.styleFrom(
        //       backgroundColor: Colors.blue,
        //       foregroundColor: Colors.white,
        //       padding: const EdgeInsets.symmetric(vertical: 12),
        //       shape: RoundedRectangleBorder(
        //         borderRadius: BorderRadius.circular(8),
        //       ),
        //     ),
        //   ),
        // ),

        // const SizedBox(height: 12),
        // SizedBox(
        //   width: double.infinity,
        //   child: OutlinedButton.icon(
        //     onPressed: () => _previewDocument(document),
        //     icon: const Icon(Icons.visibility),
        //     label: const Text('Просмотреть документ'),
        //     style: OutlinedButton.styleFrom(
        //       foregroundColor: Colors.blue,
        //       padding: const EdgeInsets.symmetric(vertical: 12),
        //       shape: RoundedRectangleBorder(
        //         borderRadius: BorderRadius.circular(8),
        //       ),
        //     ),
        //   ),
        // ),

      ],
    );
  }

  Widget _buildStatusIndicator(LabTest test, Color statusColor) {
    final isNormal = test.status == LabTestStatus.normal;

    return SizedBox(
      width: 24,
      height: 24,
      child: Center(
        child: isNormal
            ? Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: statusColor,
            shape: BoxShape.circle,
          ),
        )
            : Text(
          test.status == LabTestStatus.elevated ? '▲' : '▼',
          style: TextStyle(
            fontSize: 16,
            color: statusColor,
            fontWeight: FontWeight.w900, // жирная стрелка
          ),
        ),
      ),
    );
  }

  Color _getStatusColor(LabTestStatus status) {
    switch (status) {
      case LabTestStatus.normal:
        return Colors.green;
      case LabTestStatus.elevated:
        return Colors.orange;
      case LabTestStatus.low:
        return Colors.orange;
      case LabTestStatus.other:
        return Colors.blue;
    }
  }

  String _getDisplayValue(LabTest test) {
    // Если есть численное значение и единицы измерения, показываем их
    if (test.value.value.isNotEmpty && test.value.unitLabel.isNotEmpty) {
      // Проверяем, является ли значение числом
      final numericValue = double.tryParse(test.value.value);
      if (numericValue != null) {
        return '${test.value.value} ${test.value.unitLabel}';
      }
    }

    // Если нет численного значения, показываем статус
    return _getStatusText(test.status);
  }

  String _getStatusText(LabTestStatus status) {
    switch (status) {
      case LabTestStatus.normal:
        return 'Не обнаружено';
      case LabTestStatus.elevated:
        return 'Повышен';
      case LabTestStatus.low:
        return 'Понижен';
      case LabTestStatus.other:
        return 'Прочее';
    }
  }

  void _showTestDetail(LabTest test) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => LabTestDetailScreen(labTest: test),
      ),
    );
  }
}
