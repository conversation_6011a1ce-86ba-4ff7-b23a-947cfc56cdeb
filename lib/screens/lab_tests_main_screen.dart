import 'package:flutter/material.dart';
import '../medstata_api.dart';
import 'lab_test_detail_screen.dart';
import 'lab_reports_screen.dart';

/// Версия LabTestsScreen для использования в MainNavigationScreen
/// Без Scaffold и кнопки "Назад" для корректной интеграции с нижней навигацией
class LabTestsMainScreen extends StatefulWidget {
  const LabTestsMainScreen({super.key});

  @override
  State<LabTestsMainScreen> createState() => _LabTestsMainScreenState();
}

class _LabTestsMainScreenState extends State<LabTestsMainScreen> {
  final LabTestService _labTestService = LabTestService();
  GroupedLabTests? _labTestsData;
  bool _isLoading = true;
  String? _errorMessage;
  bool _showOnlyAbnormal = false;
  int _selectedTabIndex = 0; // 0 - Биомаркеры, 1 - До<PERSON>ументы

  @override
  void initState() {
    super.initState();
    _loadLabTests();
  }

  @override
  void dispose() {
    _labTestService.dispose();
    super.dispose();
  }

  /// Загружает лабораторные анализы
  Future<void> _loadLabTests() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final response = await _labTestService.getLabTests();
      if (mounted) {
        setState(() {
          _labTestsData = response.data;
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('❌ Ошибка загрузки анализов: $e');
      if (mounted) {
        setState(() {
          _errorMessage = 'Ошибка загрузки данных: $e';
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.grey.shade50,
      child: Column(
        children: [
          _buildHeader(),
          _buildTabBar(),
          if (_selectedTabIndex == 0) _buildPeriodSelector(),
          Expanded(child: _buildBody()),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          const Spacer(),
          IconButton(
            onPressed: () {},
            icon: const Icon(Icons.search, color: Colors.red),
          ),
          IconButton(
            onPressed: _showFilterBottomSheet,
            icon: const Icon(Icons.more_horiz),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.grey.shade200,
        borderRadius: BorderRadius.circular(25),
      ),
      child: Row(
        children: [
          Expanded(
            child: GestureDetector(
              onTap: () {
                setState(() {
                  _selectedTabIndex = 0;
                });
              },
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: _selectedTabIndex == 0
                    ? BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(25),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      )
                    : null,
                child: Text(
                  'Биомаркеры',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontWeight: _selectedTabIndex == 0 ? FontWeight.w600 : FontWeight.w500,
                    fontSize: 16,
                    color: _selectedTabIndex == 0 ? Colors.black : Colors.grey.shade600,
                  ),
                ),
              ),
            ),
          ),
          Expanded(
            child: GestureDetector(
              onTap: () {
                setState(() {
                  _selectedTabIndex = 1;
                });
              },
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: _selectedTabIndex == 1
                    ? BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(25),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      )
                    : null,
                child: Text(
                  'Документы',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontWeight: _selectedTabIndex == 1 ? FontWeight.w600 : FontWeight.w500,
                    fontSize: 16,
                    color: _selectedTabIndex == 1 ? Colors.black : Colors.grey.shade600,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPeriodSelector() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          const Text(
            'За последний год',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const Icon(Icons.keyboard_arrow_down),
          const Spacer(),
          IconButton(
            onPressed: _loadLabTests,
            icon: const Icon(Icons.refresh, color: Colors.red),
          ),
        ],
      ),
    );
  }

  Widget _buildBody() {
    if (_selectedTabIndex == 1) {
      // Показываем экран документов
      return _buildDocumentsContent();
    }

    // Показываем экран биомаркеров (оригинальная логика)
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Загрузка анализов...'),
          ],
        ),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadLabTests,
              child: const Text('Повторить'),
            ),
          ],
        ),
      );
    }

    if (_labTestsData == null || _labTestsData!.groupLabTests.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.science_outlined,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'Нет данных об анализах',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Загрузите результаты анализов\nчтобы увидеть их здесь',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    return _buildLabTestsList();
  }

  Widget _buildLabTestsList() {
    return ListView(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      physics: const BouncingScrollPhysics(),
      children: _buildTestsList(),
    );
  }

  List<Widget> _buildTestsList() {
    final data = _labTestsData!;
    final List<Widget> widgets = [];

    for (final entry in data.groupLabTests.entries) {
      final category = entry.key;
      final tests = entry.value;

      // Фильтруем анализы по статусу если включен фильтр
      final filteredTests = _showOnlyAbnormal
          ? tests.where((test) =>
              test.status == LabTestStatus.elevated ||
              test.status == LabTestStatus.low ||
              test.status == LabTestStatus.other
            ).toList()
          : tests;

      // Пропускаем категорию если нет анализов после фильтрации
      if (filteredTests.isEmpty) continue;

      // Добавляем заголовок категории
      widgets.add(_buildCategoryHeader(category));
      widgets.add(const SizedBox(height: 16));

      // Добавляем анализы этой категории
      for (final test in filteredTests) {
        widgets.add(_buildTestCard(test));
        widgets.add(const SizedBox(height: 12));
      }

      widgets.add(const SizedBox(height: 8));
    }

    return widgets;
  }

  Widget _buildDocumentsContent() {
    // Оборачиваем LabReportsScreen в Material для корректной работы DropdownButton
    return Material(
      color: Colors.transparent,
      child: const LabReportsScreen(),
    );
  }

  Widget _buildDocumentsTab() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.description_outlined,
            size: 64,
            color: Colors.grey,
          ),
          SizedBox(height: 16),
          Text(
            'Документы',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: Colors.grey,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'Здесь будут отображаться\nваши медицинские документы',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryHeader(String category) {
    return Padding(
      padding: const EdgeInsets.only(left: 4, bottom: 8),
      child: Text(
        category.toUpperCase(),
        style: TextStyle(
          fontSize: 13,
          fontWeight: FontWeight.w600,
          color: Colors.grey.shade600,
          letterSpacing: 0.5,
        ),
      ),
    );
  }

  Widget _buildTestCard(LabTest test) {
    final statusColor = _getStatusColor(test.status);

    return InkWell(
      onTap: () => _showTestDetail(test),
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            // График/индикатор слева
            _buildTestIndicator(test),
            const SizedBox(width: 16),
            // Информация о тесте
            Expanded(
              child: Text(
                test.name,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            // Значение и статус справа
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      '${test.value.value} ${test.value.unitLabel}',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: statusColor,
                      ),
                    ),
                    const SizedBox(width: 8),
                    _buildStatusIndicatorIcon(test, statusColor),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  _formatDate(test.performedAt),
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  String _formatDate(String dateString) {
    try {
      // Пытаемся парсить разные форматы дат
      if (dateString.contains('мар') || dateString.contains('янв') || dateString.contains('нояб')) {
        return dateString; // Уже в нужном формате
      }

      final date = DateTime.parse(dateString);
      final months = [
        'янв', 'фев', 'мар', 'апр', 'май', 'июн',
        'июл', 'авг', 'сен', 'окт', 'ноя', 'дек'
      ];
      return '${date.day} ${months[date.month - 1]} ${date.year}';
    } catch (e) {
      return dateString;
    }
  }


  Widget _buildStatusIndicatorIcon(LabTest test, Color statusColor) {
    final isNormal = test.status == LabTestStatus.normal;

    return SizedBox(
      width: 24,
      height: 24,
      child: isNormal
          ? Icon(
              Icons.check_circle,
              color: statusColor,
              size: 20,
            )
          : Container(
              decoration: BoxDecoration(
                color: statusColor,
                shape: BoxShape.circle,
              ),
              child: Icon(
                test.status == LabTestStatus.elevated
                    ? Icons.keyboard_arrow_up
                    : test.status == LabTestStatus.low
                        ? Icons.keyboard_arrow_down
                        : Icons.warning,
                color: Colors.white,
                size: 16,
              ),
            ),
    );
  }

  Widget _buildTestIndicator(LabTest test) {
    final statusColor = _getStatusColor(test.status);
    final isNormal = test.status == LabTestStatus.normal;

    return Container(
      width: 60,
      height: 40,
      decoration: BoxDecoration(
        color: statusColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Center(
        child: isNormal
            ? Icon(
                Icons.trending_flat,
                color: statusColor,
                size: 24,
              )
            : Icon(
                test.status == LabTestStatus.elevated
                    ? Icons.trending_up
                    : Icons.trending_down,
                color: statusColor,
                size: 24,
              ),
      ),
    );
  }

  Color _getStatusColor(LabTestStatus status) {
    switch (status) {
      case LabTestStatus.normal:
        return Colors.green;
      case LabTestStatus.elevated:
        return Colors.red;
      case LabTestStatus.low:
        return Colors.orange;
      case LabTestStatus.other:
        return Colors.red;
    }
  }

  void _showTestDetail(LabTest test) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => LabTestDetailScreen(labTest: test),
      ),
    );
  }

  void _showFilterBottomSheet() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(top: 12),
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),
            const Text(
              'Фильтры',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 20),
            SwitchListTile(
              title: const Text('Показывать только отклонения'),
              subtitle: const Text('Скрыть анализы в пределах нормы'),
              value: _showOnlyAbnormal,
              onChanged: (value) {
                setState(() {
                  _showOnlyAbnormal = value;
                });
                Navigator.pop(context);
              },
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }
}
