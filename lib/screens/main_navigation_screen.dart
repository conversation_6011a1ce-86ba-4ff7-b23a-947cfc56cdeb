import 'package:flutter/material.dart';
import 'lab_tests_main_screen.dart';
import 'upload_screen.dart';
import 'settings_screen.dart';
import 'auth_screen.dart';
import '../services/auth_state_service.dart';

class MainNavigationScreen extends StatefulWidget {
  const MainNavigationScreen({super.key});

  @override
  State<MainNavigationScreen> createState() => _MainNavigationScreenState();
}

class _MainNavigationScreenState extends State<MainNavigationScreen> {
  int _currentIndex = 0;
  final AuthStateService _authStateService = AuthStateService();

  // Список экранов для навигации
  final List<Widget> _screens = [
    const LabTestsMainScreen(),
    const UploadScreen(),
    const SettingsScreen(),
  ];

  // Заголовки для AppBar
  final List<String> _titles = [
    'Анализы',
    'Загрузить',
    'Настройки',
  ];

  @override
  void initState() {
    super.initState();
    // Подписываемся на изменения состояния авторизации
    _authStateService.addListener(_onAuthStateChanged);
  }

  @override
  void dispose() {
    _authStateService.removeListener(_onAuthStateChanged);
    super.dispose();
  }

  /// Обработчик изменения состояния авторизации
  void _onAuthStateChanged() {
    final isAuthenticated = _authStateService.isAuthenticated;
    print('🔄 MainNavigationScreen: Состояние авторизации изменилось: $isAuthenticated');

    // Если пользователь вышел из системы, переходим к экрану авторизации
    if (!isAuthenticated && mounted) {
      print('🚪 MainNavigationScreen: Пользователь вышел, переходим к AuthScreen');
      Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(builder: (context) => const AuthScreen()),
        (route) => false, // Удаляем все предыдущие экраны
      );
    }
  }

  void _onTabTapped(int index) {
    setState(() {
      _currentIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          _titles[_currentIndex],
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        // Добавляем тонкую границу снизу
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(1),
          child: Container(
            height: 1,
            color: Colors.grey.shade200,
          ),
        ),
      ),
      body: IndexedStack(
        index: _currentIndex,
        children: _screens,
      ),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildNavItem(
                  index: 0,
                  icon: Icons.science_outlined,
                  activeIcon: Icons.science,
                  label: 'Анализы',
                ),
                _buildNavItem(
                  index: 1,
                  icon: Icons.add_outlined,
                  activeIcon: Icons.add,
                  label: 'Загрузить',
                ),
                _buildNavItem(
                  index: 2,
                  icon: Icons.settings_outlined,
                  activeIcon: Icons.settings,
                  label: 'Настройки',
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNavItem({
    required int index,
    required IconData icon,
    required IconData activeIcon,
    required String label,
  }) {
    final bool isActive = _currentIndex == index;

    return Material(
      color: Colors.transparent,
      child: ConstrainedBox(
        constraints: const BoxConstraints(minHeight: 56),
        child: InkWell(
          onTap: () => _onTabTapped(index),
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  isActive ? activeIcon : icon,
                  color: isActive ? Colors.blue.shade600 : Colors.grey.shade500,
                  size: 24,
                ),
                const SizedBox(height: 4),
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: isActive ? FontWeight.w600 : FontWeight.w400,
                    color: isActive ? Colors.blue.shade600 : Colors.grey.shade500,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }


}
